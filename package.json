{"name": "string-calculator", "version": "1.0.0", "description": "This project is a simple implementation of the String Calculator kata using Test-Driven Development (TDD).", "main": "index.js", "scripts": {"build": "tsc", "test": "mocha -r ts-node/register test/**/*.test.ts"}, "repository": {"type": "git", "url": "git+https://github.com/MayankAgrawal94/string-calculator.git"}, "keywords": ["String", "calculator", "TDD", "<PERSON><PERSON>"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/MayankAgrawal94/string-calculator/issues"}, "homepage": "https://github.com/MayankAgrawal94/string-calculator#readme", "devDependencies": {"@types/chai": "^4.3.0", "@types/mocha": "^10.0.1", "chai": "^4.3.7", "mocha": "^10.2.0", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}